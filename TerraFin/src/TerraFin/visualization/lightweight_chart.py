import asyncio
import threading
from queue import Queue
from typing import List

import webview
from lightweight_charts import abstract
from lightweight_charts.util import FLOAT, parse_event_message


"""
Architecture of lightweight_charts:
Python Code → WebviewHandler → Queue → PyWV Process → webview.create_window()
Changed:
Python Code → WebviewHandler → webview.start() → Window Management
"""


class CallbackAPI:
    """Handles callbacks from Javascript to Python."""

    def __init__(self, emit_queue: Queue):
        self.emit_queue = emit_queue

    def callback(self, message: str):
        self.emit_queue.put(message)


class WebviewHandler:
    """
    Manages webview windows and handles the webview lifecycle properly.
    """

    def __init__(self):
        self.windows: List[webview.Window] = []
        self.emit_queue = Queue()  # For JS -> Python callbacks
        self.api = CallbackAPI(self.emit_queue)
        self.loaded_event = threading.Event()
        self.debug = False

    def create_window(self, width, height, x, y, screen, on_top, maximize, title):
        """
        Creates a pywebview window configuration. The actual window is created when webview.start() is called.
        Returns the index of the window in the windows list.
        """
        screen_obj = webview.screens[screen] if screen is not None else None
        if maximize and screen_obj:
            width, height = screen_obj.width, screen_obj.height
        elif maximize:
            # Will be handled when webview starts and screens are available
            pass

        # Create the window configuration
        window = webview.create_window(
            title,
            url=abstract.INDEX,
            js_api=self.api,
            width=width,
            height=height,
            x=x,
            y=y,
            screen=screen_obj,
            on_top=on_top,
            background_color="#000000",
        )

        # Set up the loaded event handler
        window.events.loaded += lambda: self.loaded_event.set()

        self.windows.append(window)
        return len(self.windows) - 1  # Return window index

    def start(self):
        """Start the webview engine and wait for windows to load."""
        self.loaded_event.clear()
        webview.start(debug=self.debug)

    def show(self, window_index):
        """Show a specific window."""
        if 0 <= window_index < len(self.windows):
            self.windows[window_index].show()

    def hide(self, window_index):
        """Hide a specific window."""
        if 0 <= window_index < len(self.windows):
            self.windows[window_index].hide()

    def evaluate_js(self, window_index, script):
        """Evaluate JavaScript in a specific window."""
        if 0 <= window_index < len(self.windows):
            return self.windows[window_index].evaluate_js(script)

    def exit(self):
        """Destroys all open windows."""
        for window in self.windows:
            if hasattr(window, "destroy"):
                window.destroy()
        self.windows.clear()


class TerraFinChart(abstract.AbstractChart):
    _main_window_handlers = None
    WV: WebviewHandler = WebviewHandler()

    def __init__(
        self,
        width: int = 800,
        height: int = 600,
        x: int = None,
        y: int = None,
        title: str = "",
        screen: int = None,
        on_top: bool = False,
        maximize: bool = False,
        debug: bool = False,
        toolbox: bool = False,
        inner_width: float = 1.0,
        inner_height: float = 1.0,
        scale_candles_only: bool = False,
        position: FLOAT = "left",
    ):
        # Store debug flag and set it on the webview handler
        TerraFinChart.WV.debug = debug
        self.is_alive = True

        # Create the window and store the window index
        self._window_index = TerraFinChart.WV.create_window(width, height, x, y, screen, on_top, maximize, title)

        # Create the abstract window with proper script function
        chart_window = abstract.Window(
            script_func=lambda s: TerraFinChart.WV.evaluate_js(self._window_index, s),
            js_api_code="pywebview.api.callback",
        )

        if TerraFinChart._main_window_handlers is None:
            super().__init__(chart_window, inner_width, inner_height, scale_candles_only, toolbox, position=position)
            TerraFinChart._main_window_handlers = self.win.handlers
        else:
            chart_window.handlers = TerraFinChart._main_window_handlers
            super().__init__(chart_window, inner_width, inner_height, scale_candles_only, toolbox, position=position)

    def show(self, block: bool = False):
        """
        Shows the chart window.
        :param block: blocks execution until the chart is closed.
        """
        if not self.win.loaded:
            # Start webview and wait for it to load
            TerraFinChart.WV.start()
            TerraFinChart.WV.loaded_event.wait()
            self.win.on_js_load()
        else:
            # Window already loaded, just show it
            TerraFinChart.WV.show(self._window_index)

        if block:
            asyncio.run(self.show_async())

    async def show_async(self):
        """Async version of show that handles event processing."""
        self.show(block=False)
        try:
            # Handle polygon integration if available
            try:
                from lightweight_charts import polygon

                [asyncio.create_task(self.polygon.async_set(*args)) for args in polygon._set_on_load]
            except ImportError:
                pass  # polygon module not available

            while self.is_alive:
                while TerraFinChart.WV.emit_queue.empty() and self.is_alive:
                    await asyncio.sleep(0.05)
                if not self.is_alive:
                    return

                response = TerraFinChart.WV.emit_queue.get()
                if response == "exit":
                    TerraFinChart.WV.exit()
                    self.is_alive = False
                    return
                else:
                    func, args = parse_event_message(self.win, response)
                    if asyncio.iscoroutinefunction(func):
                        await func(*args)
                    else:
                        func(*args)
        except KeyboardInterrupt:
            return

    def hide(self):
        """Hides the chart window."""
        TerraFinChart.WV.hide(self._window_index)

    def exit(self):
        """Exits and destroys the chart window."""
        TerraFinChart.WV.exit()
        self.is_alive = False
