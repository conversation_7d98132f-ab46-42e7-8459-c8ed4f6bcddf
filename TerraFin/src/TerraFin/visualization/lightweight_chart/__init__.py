"""
This module provides a web-compatible version of lightweight charts that works
in Streamlit applications without requiring pywebview or desktop dependencies.
"""

import os
from typing import Any, Dict, Optional

import pandas as pd
import streamlit as st
import streamlit.components.v1 as components


parent_dir = os.path.dirname(os.path.abspath(__file__))
build_dir = os.path.join(parent_dir, "frontend/build")
_component_func = components.declare_component("terrafin_lightweight_chart", path=build_dir)


class TerraFinChart:
    """
    A Streamlit-compatible lightweight charts implementation for TerraFin.

    This class provides an easy-to-use interface for creating interactive
    financial charts in Streamlit applications.
    """

    def __init__(self, width: int = 800, height: int = 600, theme: str = "dark", key: Optional[str] = None):
        """
        Initialize a TerraFinChart.

        Args:
            width: Chart width in pixels
            height: Chart height in pixels
            theme: Chart theme ("dark" or "light")
            key: Streamlit component key for state management
        """
        self.width = width
        self.height = height
        self.theme = theme
        self.key = key or f"terrafin_chart_{id(self)}"
        self.data = None
        self.chart_type = "candlestick"
        self.options = {}

    def set(self, data: pd.DataFrame, chart_type: str = "candlestick"):
        """
        Set the data for the chart.

        Args:
            data: DataFrame with time series data
            chart_type: Type of chart ("candlestick", "line", "area", "histogram")
        """
        # Convert DataFrame to JSON-serializable format
        self.data = self._prepare_data(data)
        self.chart_type = chart_type
        return self

    def _prepare_data(self, df: pd.DataFrame) -> list:
        """
        Convert DataFrame to lightweight-charts format with JSON-serializable values.

        Args:
            df: DataFrame with time series data

        Returns:
            List of data points in lightweight-charts format
        """
        if df is None or df.empty:
            return []

        data_list = []

        # Create a copy to avoid modifying the original
        df_copy = df.copy()

        # Handle time column - convert to string format
        time_cols = ["time", "Time", "date", "Date"]
        time_col = None
        for col in time_cols:
            if col in df_copy.columns:
                time_col = col
                break

        if time_col is None and len(df_copy.columns) > 0:
            # Use first column as time if no explicit time column found
            time_col = df_copy.columns[0]

        if time_col:
            # Convert timestamps to string format
            df_copy[time_col] = pd.to_datetime(df_copy[time_col]).dt.strftime("%Y-%m-%d")

        # Convert to records and ensure all values are JSON serializable
        for _, row in df_copy.iterrows():
            data_point = {}
            for col, value in row.items():
                if pd.isna(value):
                    continue  # Skip NaN values
                elif isinstance(value, (pd.Timestamp, pd.DatetimeIndex)):
                    # Convert any remaining timestamps
                    data_point[col] = pd.to_datetime(value).strftime("%Y-%m-%d")
                elif isinstance(value, (int, float)):
                    # Ensure numeric values are JSON serializable
                    data_point[col] = float(value) if not pd.isna(value) else None
                else:
                    # Convert other types to string
                    data_point[col] = str(value)

            if data_point:  # Only add non-empty data points
                data_list.append(data_point)

        return data_list

    def show(self) -> Optional[Dict[str, Any]]:
        """
        Display the chart in Streamlit.

        Returns:
            Component value containing any user interactions
        """
        if self.data is None:
            st.error("No data set. Please call .set() with your data first.")
            return None

        # Render the Streamlit component
        component_value = _component_func(
            data=self.data,
            chart_type=self.chart_type,
            width=self.width,
            height=self.height,
            theme=self.theme,
            options=self.options,
            key=self.key,
            default=None,
        )

        return component_value

    def set_options(self, **options):
        """
        Set chart options.

        Args:
            **options: Chart configuration options
        """
        self.options.update(options)
        return self
