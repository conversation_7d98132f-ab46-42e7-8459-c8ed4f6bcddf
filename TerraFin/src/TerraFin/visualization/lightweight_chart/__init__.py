"""
TerraFin Lightweight Charts implementation using Streamlit components.

This module provides a web-compatible version of lightweight charts that works
in Streamlit applications without requiring pywebview or desktop dependencies.
"""

import os
import streamlit as st
import streamlit.components.v1 as components
from typing import Optional, Dict, Any
import pandas as pd

# Determine if we're in development or production mode
_RELEASE = True  # Set to False for development

if not _RELEASE:
    # Development mode - use the React dev server
    _component_func = components.declare_component(
        "terrafin_lightweight_chart",
        url="http://localhost:3003",  # Different port from economy calendar
    )
else:
    # Production mode - use built static files
    parent_dir = os.path.dirname(os.path.abspath(__file__))
    build_dir = os.path.join(parent_dir, "frontend/build")
    _component_func = components.declare_component("terrafin_lightweight_chart", path=build_dir)


class TerraFinChart:
    """
    A Streamlit-compatible lightweight charts implementation for TerraFin.

    This class provides an easy-to-use interface for creating interactive
    financial charts in Streamlit applications.
    """

    def __init__(self, width: int = 800, height: int = 600, theme: str = "dark", key: Optional[str] = None):
        """
        Initialize a TerraFinChart.

        Args:
            width: Chart width in pixels
            height: Chart height in pixels
            theme: Chart theme ("dark" or "light")
            key: Streamlit component key for state management
        """
        self.width = width
        self.height = height
        self.theme = theme
        self.key = key or f"terrafin_chart_{id(self)}"
        self.data = None
        self.chart_type = "candlestick"
        self.options = {}

    def set(self, data: pd.DataFrame, chart_type: str = "candlestick"):
        """
        Set the data for the chart.

        Args:
            data: DataFrame with time series data
            chart_type: Type of chart ("candlestick", "line", "area", "histogram")
        """
        self.data = data
        self.chart_type = chart_type
        return self

    def show(self) -> Optional[Dict[str, Any]]:
        """
        Display the chart in Streamlit.

        Returns:
            Component value containing any user interactions
        """
        if self.data is None:
            st.error("No data set. Please call .set() with your data first.")
            return None

        # Convert DataFrame to the format expected by lightweight-charts
        chart_data = self._prepare_data()

        # Render the Streamlit component
        component_value = _component_func(
            data=chart_data,
            chart_type=self.chart_type,
            width=self.width,
            height=self.height,
            theme=self.theme,
            options=self.options,
            key=self.key,
            default=None,
        )

        return component_value

    def _prepare_data(self) -> list:
        """
        Convert DataFrame to lightweight-charts format.

        Returns:
            List of data points in lightweight-charts format
        """
        if self.data is None:
            return []

        data_list = []

        # Handle different data formats
        if self.chart_type == "candlestick":
            # Expect columns: time, open, high, low, close
            required_cols = ["time", "open", "high", "low", "close"]
            if not all(col in self.data.columns for col in required_cols):
                # Try alternative column names
                col_mapping = {
                    "Date": "time",
                    "date": "time",
                    "Time": "time",
                    "Open": "open",
                    "High": "high",
                    "Low": "low",
                    "Close": "close",
                }
                df = self.data.rename(columns=col_mapping)
            else:
                df = self.data

            for _, row in df.iterrows():
                data_point = {
                    "time": self._format_time(row["time"]),
                    "open": float(row["open"]),
                    "high": float(row["high"]),
                    "low": float(row["low"]),
                    "close": float(row["close"]),
                }
                data_list.append(data_point)

        elif self.chart_type in ["line", "area"]:
            # Expect columns: time, value (or close)
            time_col = "time" if "time" in self.data.columns else self.data.columns[0]
            value_col = "close" if "close" in self.data.columns else self.data.columns[-1]

            for _, row in self.data.iterrows():
                data_point = {"time": self._format_time(row[time_col]), "value": float(row[value_col])}
                data_list.append(data_point)

        elif self.chart_type == "histogram":
            # For volume or other histogram data
            time_col = "time" if "time" in self.data.columns else self.data.columns[0]
            value_col = "volume" if "volume" in self.data.columns else self.data.columns[-1]

            for _, row in self.data.iterrows():
                data_point = {"time": self._format_time(row[time_col]), "value": float(row[value_col])}
                data_list.append(data_point)

        return data_list

    def _format_time(self, time_value) -> str:
        """
        Format time value for lightweight-charts.

        Args:
            time_value: Time value (string, datetime, or timestamp)

        Returns:
            Formatted time string
        """
        if pd.isna(time_value):
            return ""

        # Convert to pandas datetime if not already
        if not isinstance(time_value, pd.Timestamp):
            time_value = pd.to_datetime(time_value)

        # Return in YYYY-MM-DD format for daily data
        return time_value.strftime("%Y-%m-%d")

    def set_options(self, **options):
        """
        Set chart options.

        Args:
            **options: Chart configuration options
        """
        self.options.update(options)
        return self
